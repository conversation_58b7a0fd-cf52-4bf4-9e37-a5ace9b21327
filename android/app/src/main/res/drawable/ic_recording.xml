<vector xmlns:android="http://schemas.android.com/apk/res/android"
    android:width="24dp"
    android:height="24dp"
    android:viewportWidth="24"
    android:viewportHeight="24"
    android:tint="@color/recording_red">
  <!-- Microphone body -->
  <path
      android:fillColor="@color/recording_red"
      android:pathData="M12,14c1.66,0 2.99,-1.34 2.99,-3L15,5c0,-1.66 -1.34,-3 -3,-3S9,3.34 9,5v6c0,1.66 1.34,3 3,3z"/>
  <!-- Microphone stand -->
  <path
      android:fillColor="@color/recording_red"
      android:pathData="M17.3,11c0,3 -2.54,5.1 -5.3,5.1S6.7,14 6.7,11H5c0,3.41 2.72,6.23 6,6.72V21h2v-3.28c3.28,-0.48 6,-3.3 6,-6.72h-1.7z"/>
  <!-- Recording indicator dot (converted from circle to path) -->
  <path
      android:fillColor="@color/recording_red"
      android:pathData="M19,2c1.66,0 3,1.34 3,3s-1.34,3 -3,3s-3,-1.34 -3,-3S17.34,2 19,2z"/>
</vector>