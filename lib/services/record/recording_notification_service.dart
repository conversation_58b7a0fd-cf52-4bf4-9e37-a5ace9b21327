import 'dart:async';
import 'dart:io';
import 'package:flutter_foreground_task/flutter_foreground_task.dart';
import 'package:sluqe/services/record/audio_service_manager.dart';

class RecordingNotificationService {
  static RecordingNotificationService? _instance;
  static RecordingNotificationService get instance =>
      _instance ??= RecordingNotificationService._();

  RecordingNotificationService._();

  bool _isRecording = false;
  Duration _recordingDuration = Duration.zero;

  Timer? _updateTimer;

  Future<void> initialize() async {
    print('RecordingNotificationService: Initializing...');

    await AudioServiceManager.instance.initialize();

    print('RecordingNotificationService: Initialization complete');
  }

  Future<void> startRecordingNotification({
    required Function() onStopPressed,
  }) async {
    _isRecording = true;
    _recordingDuration = Duration.zero;

    print('RecordingNotificationService: Starting recording notification...');

    bool audioServiceStarted = false;
    if (AudioServiceManager.instance.isInitialized) {
      audioServiceStarted = await AudioServiceManager.instance
          .startRecordingNotification(onStopPressed: onStopPressed);
      print(
        'RecordingNotificationService: Audio service started: $audioServiceStarted',
      );
    }

    if (!audioServiceStarted) {
      await _startEnhancedForegroundNotification();
    }

    _startUpdateTimer();
  }

  Future<void> _startEnhancedForegroundNotification() async {
    print(
      'RecordingNotificationService: Starting enhanced foreground notification',
    );

    final formattedDuration = _formatDuration(_recordingDuration);

    if (await FlutterForegroundTask.isRunningService == true) {
      await FlutterForegroundTask.restartService();
    } else {
      await FlutterForegroundTask.startService(
        notificationTitle: '🔴 Recording Audio',
        notificationText: 'Duration: $formattedDuration • Tap to return to app',
        notificationInitialRoute: '/home',
        notificationButtons: [], // Remove the stop button - use audio_service instead
        callback: _foregroundTaskCallback,
      );
    }
  }

  void updateRecordingDuration(Duration duration) {
    if (!_isRecording) return;

    _recordingDuration = duration;

    if (AudioServiceManager.instance.isInitialized) {
      AudioServiceManager.instance.updateRecordingDuration(duration);
    } else {
      _updateForegroundNotification();
    }
  }

  void _updateForegroundNotification() {}

  void _startUpdateTimer() {
    _updateTimer?.cancel();
    _updateTimer = Timer.periodic(Duration(seconds: 5), (timer) {
      if (!_isRecording) {
        timer.cancel();
        return;
      }

      if (!AudioServiceManager.instance.isInitialized) {
        final formattedDuration = _formatDuration(_recordingDuration);
        FlutterForegroundTask.updateService(
          notificationTitle: '🔴 Recording Audio',
          notificationText:
              'Duration: $formattedDuration • Tap to return to app',
        );
      }
    });
  }

  Future<void> stopRecordingNotification() async {
    _isRecording = false;
    _updateTimer?.cancel();
    _updateTimer = null;

    print('RecordingNotificationService: Stopping recording notification');

    if (AudioServiceManager.instance.isInitialized) {
      await AudioServiceManager.instance.stopRecordingNotification();
    }

    FlutterForegroundTask.stopService();
  }

  String _formatDuration(Duration duration) {
    String twoDigits(int n) => n.toString().padLeft(2, '0');
    final hours = duration.inHours;
    final minutes = duration.inMinutes.remainder(60);
    final seconds = duration.inSeconds.remainder(60);

    if (hours > 0) {
      return '$hours:${twoDigits(minutes)}:${twoDigits(seconds)}';
    } else {
      return '${twoDigits(minutes)}:${twoDigits(seconds)}';
    }
  }

  bool get isRecording => _isRecording;

  Duration get recordingDuration => _recordingDuration;

  bool get isAvailable => Platform.isIOS || Platform.isAndroid;

  bool get isInitialized => true; // Always available as fallback
}

@pragma('vm:entry-point')
void _foregroundTaskCallback() {
  FlutterForegroundTask.setTaskHandler(_RecordingTaskHandler());
}

void sendDataToTask(Map<String, dynamic> data) {
  FlutterForegroundTask.sendDataToTask(data);
}

class _RecordingTaskHandler extends TaskHandler {
  // No button handling needed - using audio_service controls instead

  @override
  Future<void> onStart(DateTime timestamp, TaskStarter starter) async {
    print('Recording task started');
  }

  @override
  void onRepeatEvent(DateTime timestamp) {}

  @override
  Future<void> onDestroy(DateTime timestamp, bool isTimeout) async {
    print('Recording task destroyed');
  }
}
