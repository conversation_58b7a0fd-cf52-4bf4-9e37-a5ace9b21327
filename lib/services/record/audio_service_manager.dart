import 'dart:async';
import 'dart:io';
import 'package:audio_service/audio_service.dart';
import 'package:sluqe/services/record/audio_service_handler.dart';

class AudioServiceManager {
  static AudioServiceManager? _instance;
  static AudioServiceManager get instance =>
      _instance ??= AudioServiceManager._();

  AudioServiceManager._();

  RecordingAudioHandler? _audioHandler;
  bool _isInitialized = false;
  static bool _initializationAttempted = false;

  Future<RecordingAudioHandler?> initialize() async {
    if (_isInitialized && _audioHandler != null) {
      print(
        'AudioServiceManager: Already initialized, returning existing handler',
      );
      return _audioHandler;
    }

    if (_initializationAttempted) {
      print('AudioServiceManager: Initialization already attempted');
      return _audioHandler;
    }

    _initializationAttempted = true;

    if (!Platform.isIOS && !Platform.isAndroid) {
      print('AudioServiceManager: Platform not supported');
      return null;
    }

    try {
      print('AudioServiceManager: Initializing AudioService...');

      _audioHandler = await AudioService.init(
        builder: () => RecordingAudioHandler(),
        config: AudioServiceConfig(
          androidNotificationChannelId: 'io.boet.sluqe.recording',
          androidNotificationChannelName: 'Audio Recording',
          androidNotificationChannelDescription:
              'Recording audio in background',
          androidNotificationOngoing: true,
          androidStopForegroundOnPause: true,
          androidNotificationClickStartsActivity: true,
          androidNotificationIcon: 'drawable/ic_notification',
          androidShowNotificationBadge: true,
          // iOS specific settings - disable artwork and media features
          preloadArtwork: false,
          artDownscaleWidth: 0,
          artDownscaleHeight: 0,
        ),
      );

      _isInitialized = true;
      print('AudioServiceManager: Successfully initialized AudioService');
      return _audioHandler;
    } catch (e) {
      print('AudioServiceManager: Failed to initialize AudioService: $e');
      _isInitialized = false;
      return null;
    }
  }

  RecordingAudioHandler? get audioHandler => _audioHandler;

  bool get isInitialized => _isInitialized && _audioHandler != null;

  bool get isAvailable => Platform.isIOS || Platform.isAndroid;

  Future<bool> startRecordingNotification({
    required Function() onStopPressed,
  }) async {
    if (!isInitialized) {
      print(
        'AudioServiceManager: Not initialized, cannot start recording notification',
      );
      return false;
    }

    try {
      await _audioHandler!.startRecording(onStopPressed: onStopPressed);
      print('AudioServiceManager: Recording notification started');
      return true;
    } catch (e) {
      print('AudioServiceManager: Failed to start recording notification: $e');
      return false;
    }
  }

  void updateRecordingDuration(Duration duration) {
    if (!isInitialized) return;

    try {
      _audioHandler!.updateRecordingDuration(duration);
    } catch (e) {
      print('AudioServiceManager: Failed to update recording duration: $e');
    }
  }

  Future<void> stopRecordingNotification() async {
    if (!isInitialized) return;

    try {
      await _audioHandler!.stopRecording();
      print('AudioServiceManager: Recording notification stopped');
    } catch (e) {
      print('AudioServiceManager: Failed to stop recording notification: $e');
    }
  }

  bool get isRecording => _audioHandler?.isRecording ?? false;

  Duration get recordingDuration =>
      _audioHandler?.recordingDuration ?? Duration.zero;

  String formatDuration(Duration duration) {
    String twoDigits(int n) => n.toString().padLeft(2, '0');
    final hours = duration.inHours;
    final minutes = duration.inMinutes.remainder(60);
    final seconds = duration.inSeconds.remainder(60);

    if (hours > 0) {
      return '$hours:${twoDigits(minutes)}:${twoDigits(seconds)}';
    } else {
      return '${twoDigits(minutes)}:${twoDigits(seconds)}';
    }
  }
}
