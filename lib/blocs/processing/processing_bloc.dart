import 'dart:async';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:sluqe/services/audio/audio_services.dart';

part 'processing_event.dart';
part 'processing_state.dart';

class ProcessingBloc extends Bloc<ProcessingEvent, ProcessingState> {
  final AudioServices _audioServices;
  StreamSubscription? _audioStreamSubscription;
  int _lastAudioCount = 0;

  ProcessingBloc({AudioServices? audioServices})
      : _audioServices = audioServices ?? AudioServices(),
        super(ProcessingInitial()) {
    
    on<StartProcessingState>(_onStartProcessingState);
    on<StopProcessingState>(_onStopProcessingState);
    on<CheckNewAudioAdded>(_onCheckNewAudioAdded);
  }

  void _onStartProcessingState(
    StartProcessingState event,
    Emitter<ProcessingState> emit,
  ) async {
    emit(ProcessingActive());

    // Get current audio count first
    try {
      final currentAudios = await _audioServices.getAllAudios(event.userId);
      _lastAudioCount = currentAudios.length;
    } catch (e) {
      _lastAudioCount = 0;
    }

    // Start listening for new audios
    _audioStreamSubscription?.cancel();
    _audioStreamSubscription = _audioServices
        .getAllAudiosStream(event.userId)
        .listen((audios) {
      // Check if new audio was added
      if (audios.length > _lastAudioCount) {
        add(CheckNewAudioAdded());
      }
    });
  }

  void _onStopProcessingState(
    StopProcessingState event,
    Emitter<ProcessingState> emit,
  ) {
    _audioStreamSubscription?.cancel();
    emit(ProcessingInitial());
  }

  void _onCheckNewAudioAdded(
    CheckNewAudioAdded event,
    Emitter<ProcessingState> emit,
  ) {
    _audioStreamSubscription?.cancel();
    emit(ProcessingInitial());
  }

  @override
  Future<void> close() {
    _audioStreamSubscription?.cancel();
    return super.close();
  }
}
