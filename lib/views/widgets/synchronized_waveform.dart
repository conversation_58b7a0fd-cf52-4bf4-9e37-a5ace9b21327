import 'dart:math';
import 'package:flutter/material.dart';
import 'package:audio_waveforms/audio_waveforms.dart';

class SynchronizedWaveform extends StatefulWidget {
  final PlayerController playerController;
  final Size size;
  final Duration currentPosition;
  final Duration totalDuration;
  final List<double>? waveformData;
  final Color fixedWaveColor;
  final Color liveWaveColor;
  final double waveThickness;
  final bool enableSeekGesture;
  final VoidCallback? onTap;

  const SynchronizedWaveform({
    super.key,
    required this.playerController,
    required this.size,
    required this.currentPosition,
    required this.totalDuration,
    this.waveformData,
    this.fixedWaveColor = Colors.grey,
    this.liveWaveColor = Colors.black,
    this.waveThickness = 2.0,
    this.enableSeekGesture = true,
    this.onTap,
  });

  @override
  State<SynchronizedWaveform> createState() => _SynchronizedWaveformState();
}

class _SynchronizedWaveformState extends State<SynchronizedWaveform>
    with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _animation;
  List<double> _amplitudes = [];
  bool _isInitialized = false;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 200),
      vsync: this,
    );
    _animation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _animationController, curve: Curves.easeInOut),
    );
    _initializeWaveform();
  }

  @override
  void didUpdateWidget(SynchronizedWaveform oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (oldWidget.waveformData != widget.waveformData) {
      _initializeWaveform();
    }
  }

  void _initializeWaveform() async {
    try {
      // Get waveform data from the player controller
      if (widget.playerController.waveformData.isNotEmpty) {
        setState(() {
          _amplitudes = List<double>.from(widget.playerController.waveformData);
          _isInitialized = true;
        });
        _animationController.forward();
      } else if (widget.waveformData != null) {
        setState(() {
          _amplitudes = List<double>.from(widget.waveformData!);
          _isInitialized = true;
        });
        _animationController.forward();
      } else {
        // Generate placeholder waveform
        _generatePlaceholderWaveform();
      }
    } catch (e) {
      _generatePlaceholderWaveform();
    }
  }

  void _generatePlaceholderWaveform() {
    final random = Random();
    setState(() {
      _amplitudes = List.generate(100, (index) {
        return 0.1 + random.nextDouble() * 0.9; // Random amplitudes between 0.1 and 1.0
      });
      _isInitialized = true;
    });
    _animationController.forward();
  }

  double get _progressRatio {
    if (widget.totalDuration.inMilliseconds == 0) return 0.0;
    final ratio = widget.currentPosition.inMilliseconds / widget.totalDuration.inMilliseconds;
    return ratio.clamp(0.0, 1.0);
  }

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTapDown: widget.enableSeekGesture ? _onTapDown : null,
      child: SizedBox(
        width: widget.size.width,
        height: widget.size.height,
        child: AnimatedBuilder(
          animation: _animation,
          builder: (context, child) {
            return CustomPaint(
              size: widget.size,
              painter: WaveformPainter(
                amplitudes: _amplitudes,
                progressRatio: _progressRatio,
                fixedWaveColor: widget.fixedWaveColor,
                liveWaveColor: widget.liveWaveColor,
                waveThickness: widget.waveThickness,
                animationValue: _animation.value,
                isInitialized: _isInitialized,
              ),
            );
          },
        ),
      ),
    );
  }

  void _onTapDown(TapDownDetails details) {
    if (!_isInitialized || widget.totalDuration.inMilliseconds == 0) return;

    final tapPosition = details.localPosition.dx;
    final progressRatio = (tapPosition / widget.size.width).clamp(0.0, 1.0);
    final seekPosition = Duration(
      milliseconds: (widget.totalDuration.inMilliseconds * progressRatio).round(),
    );

    // Seek to the tapped position
    widget.playerController.seekTo(seekPosition.inMilliseconds);
    widget.onTap?.call();
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }
}

class WaveformPainter extends CustomPainter {
  final List<double> amplitudes;
  final double progressRatio;
  final Color fixedWaveColor;
  final Color liveWaveColor;
  final double waveThickness;
  final double animationValue;
  final bool isInitialized;

  WaveformPainter({
    required this.amplitudes,
    required this.progressRatio,
    required this.fixedWaveColor,
    required this.liveWaveColor,
    required this.waveThickness,
    required this.animationValue,
    required this.isInitialized,
  });

  @override
  void paint(Canvas canvas, Size size) {
    if (!isInitialized || amplitudes.isEmpty) {
      _drawPlaceholder(canvas, size);
      return;
    }

    final paint = Paint()
      ..strokeWidth = waveThickness
      ..strokeCap = StrokeCap.round;

    final barWidth = size.width / amplitudes.length;
    final progressPosition = size.width * progressRatio;

    for (int i = 0; i < amplitudes.length; i++) {
      final x = i * barWidth + barWidth / 2;
      final amplitude = amplitudes[i] * animationValue;
      final barHeight = amplitude * size.height * 0.8; // 80% of container height
      final y1 = (size.height - barHeight) / 2;
      final y2 = y1 + barHeight;

      // Choose color based on progress
      paint.color = x <= progressPosition ? liveWaveColor : fixedWaveColor;

      canvas.drawLine(
        Offset(x, y1),
        Offset(x, y2),
        paint,
      );
    }
  }

  void _drawPlaceholder(Canvas canvas, Size size) {
    final paint = Paint()
      ..color = fixedWaveColor.withValues(alpha: 0.3)
      ..strokeWidth = waveThickness
      ..strokeCap = StrokeCap.round;

    // Draw placeholder bars
    for (int i = 0; i < 20; i++) {
      final x = (i + 0.5) * (size.width / 20);
      final height = (8 + (i % 3) * 4) * animationValue;
      final y1 = (size.height - height) / 2;
      final y2 = y1 + height;

      canvas.drawLine(
        Offset(x, y1),
        Offset(x, y2),
        paint,
      );
    }
  }

  @override
  bool shouldRepaint(WaveformPainter oldDelegate) {
    return oldDelegate.progressRatio != progressRatio ||
        oldDelegate.animationValue != animationValue ||
        oldDelegate.amplitudes != amplitudes ||
        oldDelegate.isInitialized != isInitialized;
  }
}
